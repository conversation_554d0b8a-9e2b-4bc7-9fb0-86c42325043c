import React, { useState } from 'react';
import { Upload, FileText, Eye, Download, CheckCircle } from 'lucide-react';

interface OCRResult {
  holderName: string;
  village: string;
  district: string;
  state: string;
  claimType: string;
  area: string;
  surveyNumber: string;
  confidence: number;
}

const DocumentDigitization: React.FC = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [ocrResults, setOcrResults] = useState<OCRResult | null>(null);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
      setOcrResults(null);
    }
  };

  const processDocument = async () => {
    if (!uploadedFile) return;
    
    setIsProcessing(true);
    
    // Simulate OCR processing delay
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Mock OCR results - in real implementation, this would call the backend OCR API
    const mockResults: OCRResult = {
      holderName: 'Ramesh Kumar Patel',
      village: 'Jhiripani',
      district: 'Rayagada',
      state: 'Odisha',
      claimType: 'IFR',
      area: '2.5',
      surveyNumber: 'SY-234/1A',
      confidence: 94.5
    };
    
    setOcrResults(mockResults);
    setIsProcessing(false);
  };

  const resetForm = () => {
    setUploadedFile(null);
    setOcrResults(null);
    setIsUploading(false);
    setIsProcessing(false);
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-slate-800 mb-2">Document Digitization</h1>
        <p className="text-slate-600">
          Upload FRA documents for AI-powered OCR extraction and digitization
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Upload Section */}
        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <h2 className="text-lg font-semibold text-slate-800 mb-4 flex items-center">
            <Upload className="w-5 h-5 mr-2 text-blue-600" />
            Upload Document
          </h2>
          
          {!uploadedFile ? (
            <div className="border-2 border-dashed border-slate-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors duration-200">
              <FileText className="w-12 h-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-600 mb-2">Drop your FRA document here or click to browse</p>
              <p className="text-sm text-slate-500 mb-4">Supports PDF, JPG, PNG files up to 10MB</p>
              <label className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer transition-colors duration-200">
                <Upload className="w-4 h-4 mr-2" />
                Choose File
                <input
                  type="file"
                  className="hidden"
                  accept=".pdf,.jpg,.jpeg,.png"
                  onChange={handleFileUpload}
                />
              </label>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="bg-slate-50 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <FileText className="w-8 h-8 text-blue-600" />
                  <div className="flex-1">
                    <p className="font-medium text-slate-800">{uploadedFile.name}</p>
                    <p className="text-sm text-slate-600">{(uploadedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                  </div>
                  <button
                    onClick={resetForm}
                    className="text-slate-400 hover:text-slate-600 transition-colors duration-200"
                  >
                    ×
                  </button>
                </div>
              </div>
              
              <button
                onClick={processDocument}
                disabled={isProcessing}
                className="w-full flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-slate-400 transition-all duration-200"
              >
                {isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <Eye className="w-4 h-4 mr-2" />
                    Extract Information
                  </>
                )}
              </button>
            </div>
          )}
        </div>

        {/* Results Section */}
        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <h2 className="text-lg font-semibold text-slate-800 mb-4 flex items-center">
            <CheckCircle className="w-5 h-5 mr-2 text-green-600" />
            Extraction Results
          </h2>
          
          {!ocrResults ? (
            <div className="text-center py-12">
              <FileText className="w-16 h-16 text-slate-300 mx-auto mb-4" />
              <p className="text-slate-500">Upload and process a document to see extraction results</p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Confidence Score */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-green-800">Extraction Confidence</span>
                  <span className="text-lg font-bold text-green-600">{ocrResults.confidence}%</span>
                </div>
                <div className="w-full bg-green-200 rounded-full h-2 mt-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${ocrResults.confidence}%` }}
                  ></div>
                </div>
              </div>

              {/* Extracted Fields */}
              <div className="space-y-3">
                {[
                  { label: 'Patta Holder Name', value: ocrResults.holderName },
                  { label: 'Village', value: ocrResults.village },
                  { label: 'District', value: ocrResults.district },
                  { label: 'State', value: ocrResults.state },
                  { label: 'Claim Type', value: ocrResults.claimType },
                  { label: 'Area (hectares)', value: ocrResults.area },
                  { label: 'Survey Number', value: ocrResults.surveyNumber },
                ].map((field, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-slate-100 last:border-b-0">
                    <span className="text-sm font-medium text-slate-700">{field.label}:</span>
                    <span className="text-sm text-slate-800 font-medium">{field.value}</span>
                  </div>
                ))}
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3 pt-4">
                <button className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                  <Download className="w-4 h-4 mr-2" />
                  Export Data
                </button>
                <button 
                  onClick={resetForm}
                  className="flex-1 flex items-center justify-center px-4 py-2 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors duration-200"
                >
                  Process New
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Processing Overlay */}
      {isProcessing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h3 className="text-lg font-medium text-slate-800 mb-2">Processing Document</h3>
              <p className="text-sm text-slate-600">
                AI is extracting information from your document...
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentDigitization;