import React from 'react';
import { NavLink } from 'react-router-dom';
import { Map, FileText, Satellite, Brain, Home } from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navItems = [
    { path: '/', label: 'Dashboard', icon: Home },
    { path: '/fra-atlas', label: 'FRA Atlas', icon: Map },
    { path: '/document-digitization', label: 'Document Digitization', icon: FileText },
    { path: '/asset-mapping', label: 'Asset Mapping', icon: Satellite },
    { path: '/dss', label: 'DSS', icon: Brain },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <header className="bg-white shadow-sm border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-600 to-green-600 rounded-lg">
                <Map className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-slate-800">FRA Atlas & DSS Portal</h1>
                <p className="text-sm text-slate-600">Forest Rights Act Management System</p>
              </div>
            </div>
            <div className="text-sm text-slate-600 bg-slate-100 px-3 py-1 rounded-full">
              Prototype v1.0
            </div>
          </div>
        </div>
      </header>

      <nav className="bg-white border-b border-slate-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8 overflow-x-auto">
            {navItems.map((item) => {
              const Icon = item.icon;
              return (
                <NavLink
                  key={item.path}
                  to={item.path}
                  className={({ isActive }) =>
                    `flex items-center space-x-2 py-4 px-3 border-b-2 transition-all duration-200 whitespace-nowrap ${
                      isActive
                        ? 'border-blue-600 text-blue-600 bg-blue-50'
                        : 'border-transparent text-slate-600 hover:text-blue-600 hover:bg-blue-50'
                    }`
                  }
                >
                  <Icon className="w-4 h-4" />
                  <span className="font-medium">{item.label}</span>
                </NavLink>
              );
            })}
          </div>
        </div>
      </nav>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </main>
    </div>
  );
};

export default Layout;