import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import FRAAtlas from './pages/FRAAtlas';
import DocumentDigitization from './pages/DocumentDigitization';
import AssetMapping from './pages/AssetMapping';
import DSS from './pages/DSS';
import './styles/leaflet-overrides.css';

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/fra-atlas" element={<FRAAtlas />} />
          <Route path="/document-digitization" element={<DocumentDigitization />} />
          <Route path="/asset-mapping" element={<AssetMapping />} />
          <Route path="/dss" element={<DSS />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;