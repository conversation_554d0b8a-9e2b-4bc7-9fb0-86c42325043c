import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Map, FileText, Satellite, Brain, Users, MapPin, TreePine, Award } from 'lucide-react';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();

  const stats = [
    { label: 'Total FRA Claims', value: '2,456', icon: MapPin, color: 'text-blue-600', bg: 'bg-blue-100' },
    { label: 'Documents Processed', value: '1,234', icon: FileText, color: 'text-green-600', bg: 'bg-green-100' },
    { label: 'Villages Mapped', value: '89', icon: Map, color: 'text-purple-600', bg: 'bg-purple-100' },
    { label: 'Schemes Recommended', value: '156', icon: Award, color: 'text-orange-600', bg: 'bg-orange-100' },
  ];

  const modules = [
    {
      title: 'FRA Atlas',
      description: 'Interactive WebGIS visualization of Forest Rights Act claims and boundaries',
      icon: Map,
      path: '/fra-atlas',
      color: 'from-blue-500 to-blue-600',
    },
    {
      title: 'Document Digitization',
      description: 'OCR-powered extraction of information from FRA documents',
      icon: FileText,
      path: '/document-digitization',
      color: 'from-green-500 to-green-600',
    },
    {
      title: 'Asset Mapping',
      description: 'AI-based classification and mapping of land use from satellite imagery',
      icon: Satellite,
      path: '/asset-mapping',
      color: 'from-purple-500 to-purple-600',
    },
    {
      title: 'Decision Support System',
      description: 'Intelligent recommendations for development schemes and interventions',
      icon: Brain,
      path: '/dss',
      color: 'from-orange-500 to-orange-600',
    },
  ];

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-slate-800 mb-2">
          Welcome to FRA Atlas & DSS Portal
        </h1>
        <p className="text-slate-600 max-w-2xl mx-auto">
          An integrated platform for Forest Rights Act management, combining AI-powered document processing, 
          asset mapping, and intelligent decision support for sustainable forest governance.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="bg-white rounded-lg shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow duration-200">
              <div className="flex items-center">
                <div className={`flex-shrink-0 w-12 h-12 rounded-lg ${stat.bg} flex items-center justify-center`}>
                  <Icon className={`w-6 h-6 ${stat.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-2xl font-bold text-slate-800">{stat.value}</p>
                  <p className="text-sm text-slate-600">{stat.label}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Modules Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {modules.map((module, index) => {
          const Icon = module.icon;
          return (
            <div
              key={index}
              className="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer group"
              onClick={() => navigate(module.path)}
            >
              <div className={`h-2 bg-gradient-to-r ${module.color}`}></div>
              <div className="p-6">
                <div className="flex items-start space-x-4">
                  <div className={`flex-shrink-0 w-12 h-12 bg-gradient-to-r ${module.color} rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-200`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-slate-800 group-hover:text-blue-600 transition-colors duration-200">
                      {module.title}
                    </h3>
                    <p className="text-slate-600 text-sm mt-1 leading-relaxed">
                      {module.description}
                    </p>
                    <div className="mt-4">
                      <span className="inline-flex items-center text-sm font-medium text-blue-600 group-hover:text-blue-700">
                        Explore Module
                        <svg className="ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Quick Access Section */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
        <h2 className="text-lg font-semibold text-slate-800 mb-4 flex items-center">
          <TreePine className="w-5 h-5 mr-2 text-green-600" />
          Recent Activity
        </h2>
        <div className="space-y-3">
          {[
            { text: 'New FRA claim submitted for Jhiripani Village, Odisha', time: '2 hours ago' },
            { text: 'Asset mapping completed for Keshkal region, Chhattisgarh', time: '1 day ago' },
            { text: 'DSS recommendation generated for 3 villages in Tripura', time: '2 days ago' },
          ].map((activity, index) => (
            <div key={index} className="flex items-center justify-between py-2 border-b border-slate-100 last:border-b-0">
              <p className="text-sm text-slate-700">{activity.text}</p>
              <span className="text-xs text-slate-500">{activity.time}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;