/* Leaflet CSS overrides for better integration */
.leaflet-container {
  font-family: inherit;
}

.leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.leaflet-popup-tip {
  background: white;
}

.leaflet-control-layers {
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.leaflet-control-zoom {
  border: none;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.leaflet-control-zoom a {
  border: none;
  background: white;
  color: #374151;
  font-weight: bold;
}

.leaflet-control-zoom a:hover {
  background: #F3F4F6;
  color: #1F2937;
}

.custom-marker {
  background: transparent;
  border: none;
}

/* Fix for leaflet attribution */
.leaflet-attribution-flag {
  display: none;
}