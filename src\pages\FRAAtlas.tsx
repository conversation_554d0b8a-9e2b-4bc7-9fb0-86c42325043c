import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Polygon, LayersControl } from 'react-leaflet';
import { Filter, Layers, MapPin } from 'lucide-react';
import { fraClaimsData, stateData } from '../data/mockData';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in react-leaflet
import L from 'leaflet';
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

const FRAAtlas: React.FC = () => {
  const [selectedState, setSelectedState] = useState<string>('');
  const [selectedDistrict, setSelectedDistrict] = useState<string>('');
  const [selectedVillage, setSelectedVillage] = useState<string>('');
  const [filteredClaims, setFilteredClaims] = useState(fraClaimsData);

  useEffect(() => {
    let filtered = fraClaimsData;
    
    if (selectedState) {
      filtered = filtered.filter(claim => claim.state === selectedState);
    }
    if (selectedDistrict) {
      filtered = filtered.filter(claim => claim.district === selectedDistrict);
    }
    if (selectedVillage) {
      filtered = filtered.filter(claim => claim.village === selectedVillage);
    }
    
    setFilteredClaims(filtered);
  }, [selectedState, selectedDistrict, selectedVillage]);

  const states = [...new Set(fraClaimsData.map(claim => claim.state))];
  const districts = selectedState 
    ? [...new Set(fraClaimsData.filter(claim => claim.state === selectedState).map(claim => claim.district))]
    : [];
  const villages = selectedDistrict
    ? [...new Set(fraClaimsData.filter(claim => claim.district === selectedDistrict).map(claim => claim.village))]
    : [];

  const claimTypeColors: { [key: string]: string } = {
    'IFR': '#10B981', // green
    'CFR': '#3B82F6', // blue
    'CR': '#F59E0B',  // orange
  };

  return (
    <div className="h-screen flex">
      {/* Sidebar */}
      <div className="w-80 bg-white shadow-lg border-r border-slate-200 flex flex-col">
        <div className="p-6 border-b border-slate-200">
          <h2 className="text-xl font-semibold text-slate-800 flex items-center">
            <Filter className="w-5 h-5 mr-2 text-blue-600" />
            Map Filters
          </h2>
        </div>
        
        <div className="p-6 space-y-4 flex-1 overflow-y-auto">
          {/* State Filter */}
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">State</label>
            <select
              value={selectedState}
              onChange={(e) => {
                setSelectedState(e.target.value);
                setSelectedDistrict('');
                setSelectedVillage('');
              }}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All States</option>
              {states.map(state => (
                <option key={state} value={state}>{state}</option>
              ))}
            </select>
          </div>

          {/* District Filter */}
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">District</label>
            <select
              value={selectedDistrict}
              onChange={(e) => {
                setSelectedDistrict(e.target.value);
                setSelectedVillage('');
              }}
              disabled={!selectedState}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-slate-100 disabled:text-slate-400"
            >
              <option value="">All Districts</option>
              {districts.map(district => (
                <option key={district} value={district}>{district}</option>
              ))}
            </select>
          </div>

          {/* Village Filter */}
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Village</label>
            <select
              value={selectedVillage}
              onChange={(e) => setSelectedVillage(e.target.value)}
              disabled={!selectedDistrict}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-slate-100 disabled:text-slate-400"
            >
              <option value="">All Villages</option>
              {villages.map(village => (
                <option key={village} value={village}>{village}</option>
              ))}
            </select>
          </div>

          {/* Legend */}
          <div className="mt-6 pt-6 border-t border-slate-200">
            <h3 className="text-sm font-medium text-slate-700 mb-3 flex items-center">
              <Layers className="w-4 h-4 mr-2" />
              Claim Types
            </h3>
            <div className="space-y-2">
              {Object.entries(claimTypeColors).map(([type, color]) => (
                <div key={type} className="flex items-center space-x-2">
                  <div 
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: color }}
                  ></div>
                  <span className="text-sm text-slate-600">
                    {type} - {type === 'IFR' ? 'Individual Forest Rights' : 
                              type === 'CFR' ? 'Community Forest Rights' : 
                              'Community Rights'}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Summary */}
          <div className="bg-slate-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-slate-700 mb-2">Current View</h3>
            <p className="text-xs text-slate-600">
              Showing {filteredClaims.length} claims
              {selectedState && ` in ${selectedState}`}
              {selectedDistrict && `, ${selectedDistrict}`}
              {selectedVillage && `, ${selectedVillage}`}
            </p>
          </div>
        </div>
      </div>

      {/* Map */}
      <div className="flex-1 relative">
        <MapContainer
          center={[20.5937, 78.9629]} // Center of India
          zoom={5}
          className="h-full w-full"
        >
          <LayersControl position="topright">
            <LayersControl.BaseLayer checked name="OpenStreetMap">
              <TileLayer
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              />
            </LayersControl.BaseLayer>
            
            <LayersControl.BaseLayer name="Satellite">
              <TileLayer
                attribution='&copy; <a href="https://www.esri.com/">Esri</a>'
                url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
              />
            </LayersControl.BaseLayer>

            <LayersControl.Overlay checked name="State Boundaries">
              {stateData.map((state, index) => (
                <Polygon
                  key={index}
                  positions={state.coordinates}
                  pathOptions={{
                    color: '#6B7280',
                    weight: 2,
                    fillOpacity: 0.1,
                  }}
                >
                  <Popup>
                    <div className="text-center">
                      <h3 className="font-medium">{state.name}</h3>
                      <p className="text-sm text-slate-600">State Boundary</p>
                    </div>
                  </Popup>
                </Polygon>
              ))}
            </LayersControl.Overlay>

            <LayersControl.Overlay checked name="FRA Claims">
              {filteredClaims.map((claim) => (
                <Marker
                  key={claim.id}
                  position={claim.coordinates}
                  icon={L.divIcon({
                    className: 'custom-marker',
                    html: `<div style="background-color: ${claimTypeColors[claim.claimType]}; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
                  })}
                >
                  <Popup>
                    <div className="min-w-[200px]">
                      <h3 className="font-medium text-slate-800">{claim.holderName}</h3>
                      <div className="space-y-1 mt-2 text-sm">
                        <p><span className="font-medium">Village:</span> {claim.village}</p>
                        <p><span className="font-medium">District:</span> {claim.district}</p>
                        <p><span className="font-medium">State:</span> {claim.state}</p>
                        <p><span className="font-medium">Claim Type:</span> 
                          <span 
                            className="inline-block ml-2 px-2 py-1 rounded-full text-xs font-medium text-white"
                            style={{ backgroundColor: claimTypeColors[claim.claimType] }}
                          >
                            {claim.claimType}
                          </span>
                        </p>
                        <p><span className="font-medium">Area:</span> {claim.area} hectares</p>
                        <p><span className="font-medium">Status:</span> 
                          <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                            claim.status === 'Approved' ? 'bg-green-100 text-green-800' :
                            claim.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {claim.status}
                          </span>
                        </p>
                      </div>
                    </div>
                  </Popup>
                </Marker>
              ))}
            </LayersControl.Overlay>
          </LayersControl>
        </MapContainer>
        
        {/* Map Info Panel */}
        <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-4 z-[1000] max-w-sm">
          <h3 className="font-medium text-slate-800 mb-2 flex items-center">
            <MapPin className="w-4 h-4 mr-2 text-blue-600" />
            Map Information
          </h3>
          <p className="text-sm text-slate-600">
            Interactive visualization of Forest Rights Act claims across India. 
            Use the sidebar filters to explore specific regions and claim types.
          </p>
        </div>
      </div>
    </div>
  );
};

export default FRAAtlas;