// Type definitions for the FRA Atlas application

export interface FRAClaim {
  id: string;
  holderName: string;
  village: string;
  district: string;
  state: string;
  claimType: 'IFR' | 'CFR' | 'CR';
  coordinates: [number, number];
  area: number;
  status: 'Approved' | 'Pending' | 'Rejected';
  submissionDate?: Date;
  approvalDate?: Date;
}

export interface GeoBoundary {
  name: string;
  type: 'state' | 'district' | 'village';
  coordinates: [number, number][];
  properties?: Record<string, any>;
}

export interface Village {
  id: string;
  name: string;
  district: string;
  state: string;
  stats: {
    population: number;
    forestCover: number;
    waterIndex: number;
    agriculturePercentage: number;
    literacyRate: number;
    averageIncome: number;
  };
}

export interface Scheme {
  id: string;
  name: string;
  description: string;
  ministry: string;
  eligibilityScore: number;
  targetBeneficiaries?: string[];
  budgetAllocation?: number;
}

export interface DocumentUpload {
  id: string;
  filename: string;
  uploadDate: Date;
  status: 'uploaded' | 'processing' | 'completed' | 'failed';
  extractedData?: OCRResult;
}

export interface OCRResult {
  holderName: string;
  village: string;
  district: string;
  state: string;
  claimType: string;
  area: string;
  surveyNumber: string;
  confidence: number;
}

export interface AssetClassification {
  landUseTypes: {
    forest: { percentage: number; area: number };
    water: { percentage: number; area: number };
    agricultural: { percentage: number; area: number };
    urban: { percentage: number; area: number };
    barren: { percentage: number; area: number };
  };
  confidence: number;
  processedImageUrl: string;
  analysisDate: Date;
}

export interface DSSRecommendation {
  scheme: Scheme;
  reason: string;
  priority: 'high' | 'medium' | 'low';
  impact: string;
  implementationTimeline?: string;
  estimatedBudget?: number;
}