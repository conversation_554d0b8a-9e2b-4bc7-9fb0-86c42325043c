import React, { useState } from 'react';
import { Upload, Satellite, Eye, Download, BarChart3 } from 'lucide-react';

interface ClassificationResult {
  landUseTypes: {
    forest: { percentage: number; area: number };
    water: { percentage: number; area: number };
    agricultural: { percentage: number; area: number };
    urban: { percentage: number; area: number };
    barren: { percentage: number; area: number };
  };
  confidence: number;
  processedImageUrl: string;
}

const AssetMapping: React.FC = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [classificationResults, setClassificationResults] = useState<ClassificationResult | null>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedImage(file);
      setClassificationResults(null);
    }
  };

  const processImage = async () => {
    if (!uploadedImage) return;
    
    setIsProcessing(true);
    
    // Simulate AI processing delay
    await new Promise(resolve => setTimeout(resolve, 4000));
    
    // Mock classification results - in real implementation, this would call the backend AI API
    const mockResults: ClassificationResult = {
      landUseTypes: {
        forest: { percentage: 45.2, area: 1356 },
        water: { percentage: 8.7, area: 261 },
        agricultural: { percentage: 32.1, area: 963 },
        urban: { percentage: 6.3, area: 189 },
        barren: { percentage: 7.7, area: 231 },
      },
      confidence: 91.3,
      processedImageUrl: 'https://images.pexels.com/photos/1005644/pexels-photo-1005644.jpeg?auto=compress&cs=tinysrgb&w=800'
    };
    
    setClassificationResults(mockResults);
    setIsProcessing(false);
  };

  const resetForm = () => {
    setUploadedImage(null);
    setClassificationResults(null);
    setIsUploading(false);
    setIsProcessing(false);
  };

  const landUseColors = {
    forest: '#10B981', // green
    water: '#3B82F6',  // blue
    agricultural: '#F59E0B', // yellow
    urban: '#EF4444', // red
    barren: '#9CA3AF', // gray
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-slate-800 mb-2">AI-Powered Asset Mapping</h1>
        <p className="text-slate-600">
          Upload satellite imagery for automated land-use classification and asset identification
        </p>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        {/* Upload Section */}
        <div className="xl:col-span-1">
          <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6 h-fit">
            <h2 className="text-lg font-semibold text-slate-800 mb-4 flex items-center">
              <Upload className="w-5 h-5 mr-2 text-blue-600" />
              Upload Satellite Image
            </h2>
            
            {!uploadedImage ? (
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors duration-200">
                <Satellite className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                <p className="text-slate-600 mb-2">Drop satellite image here</p>
                <p className="text-sm text-slate-500 mb-4">Supports JPG, PNG files up to 50MB</p>
                <label className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer transition-colors duration-200">
                  <Upload className="w-4 h-4 mr-2" />
                  Browse Files
                  <input
                    type="file"
                    className="hidden"
                    accept=".jpg,.jpeg,.png"
                    onChange={handleImageUpload}
                  />
                </label>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="bg-slate-50 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <Satellite className="w-8 h-8 text-blue-600" />
                    <div className="flex-1">
                      <p className="font-medium text-slate-800">{uploadedImage.name}</p>
                      <p className="text-sm text-slate-600">{(uploadedImage.size / 1024 / 1024).toFixed(2)} MB</p>
                    </div>
                  </div>
                </div>
                
                <button
                  onClick={processImage}
                  disabled={isProcessing}
                  className="w-full flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-slate-400 transition-all duration-200"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processing with AI...
                    </>
                  ) : (
                    <>
                      <Eye className="w-4 h-4 mr-2" />
                      Classify Land Use
                    </>
                  )}
                </button>
                
                <button
                  onClick={resetForm}
                  className="w-full px-4 py-2 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors duration-200"
                >
                  Upload New Image
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Results Section */}
        <div className="xl:col-span-2">
          <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
            <h2 className="text-lg font-semibold text-slate-800 mb-4 flex items-center">
              <BarChart3 className="w-5 h-5 mr-2 text-green-600" />
              Classification Results
            </h2>
            
            {!classificationResults ? (
              <div className="text-center py-12">
                <Satellite className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                <p className="text-slate-500">Upload and process an image to see AI classification results</p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Confidence Score */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-green-800">Classification Confidence</span>
                    <span className="text-xl font-bold text-green-600">{classificationResults.confidence}%</span>
                  </div>
                  <div className="w-full bg-green-200 rounded-full h-3">
                    <div 
                      className="bg-green-600 h-3 rounded-full transition-all duration-1000"
                      style={{ width: `${classificationResults.confidence}%` }}
                    ></div>
                  </div>
                </div>

                {/* Processed Image */}
                <div className="rounded-lg overflow-hidden">
                  <img
                    src={classificationResults.processedImageUrl}
                    alt="Processed satellite imagery"
                    className="w-full h-64 object-cover"
                  />
                  <div className="bg-slate-50 p-3 text-center">
                    <p className="text-sm text-slate-600">AI-Processed Classification Map</p>
                  </div>
                </div>

                {/* Land Use Breakdown */}
                <div className="space-y-3">
                  <h3 className="font-medium text-slate-800">Land Use Distribution</h3>
                  {Object.entries(classificationResults.landUseTypes).map(([type, data]) => {
                    const color = landUseColors[type as keyof typeof landUseColors];
                    return (
                      <div key={type} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div 
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: color }}
                            ></div>
                            <span className="text-sm font-medium text-slate-700 capitalize">
                              {type.replace('agricultural', 'agricultural land')}
                            </span>
                          </div>
                          <div className="text-right">
                            <span className="text-sm font-bold text-slate-800">{data.percentage}%</span>
                            <p className="text-xs text-slate-600">{data.area} hectares</p>
                          </div>
                        </div>
                        <div className="w-full bg-slate-200 rounded-full h-2">
                          <div 
                            className="h-2 rounded-full transition-all duration-1000"
                            style={{ 
                              backgroundColor: color,
                              width: `${data.percentage}%`
                            }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Export Options */}
                <div className="flex space-x-3 pt-4 border-t border-slate-200">
                  <button className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                    <Download className="w-4 h-4 mr-2" />
                    Export Report
                  </button>
                  <button className="flex-1 flex items-center justify-center px-4 py-2 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors duration-200">
                    <Satellite className="w-4 h-4 mr-2" />
                    View on Map
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Processing Overlay */}
      {isProcessing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h3 className="text-lg font-medium text-slate-800 mb-2">AI Processing</h3>
              <p className="text-sm text-slate-600">
                Analyzing satellite imagery for land-use classification...
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AssetMapping;