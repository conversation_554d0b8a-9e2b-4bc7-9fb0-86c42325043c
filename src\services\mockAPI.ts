// Mock API services that simulate backend functionality
// In a real implementation, these would make actual HTTP calls to the backend

export interface OCRRequest {
  file: File;
}

export interface OCRResponse {
  holderName: string;
  village: string;
  district: string;
  state: string;
  claimType: string;
  area: string;
  surveyNumber: string;
  confidence: number;
}

export interface AssetMappingRequest {
  imageFile: File;
}

export interface AssetMappingResponse {
  landUseClassification: {
    forest: number;
    water: number;
    agricultural: number;
    urban: number;
    barren: number;
  };
  confidence: number;
  processedImageUrl: string;
}

export interface DSSRequest {
  village: string;
}

export interface DSSResponse {
  recommendations: {
    schemeName: string;
    eligibilityScore: number;
    reasoning: string;
    priority: 'high' | 'medium' | 'low';
  }[];
}

// Mock OCR API
export const processOCR = async (request: OCRRequest): Promise<OCRResponse> => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Return mock OCR results
  // TODO: Integrate with actual OCR service (Tesseract.js, Google Vision API, etc.)
  return {
    holderName: '<PERSON><PERSON>',
    village: 'Jhiripani',
    district: 'Rayagada',
    state: 'Odisha',
    claimType: 'IFR',
    area: '2.5',
    surveyNumber: 'SY-234/1A',
    confidence: 94.5
  };
};

// Mock Asset Mapping API
export const processAssetMapping = async (request: AssetMappingRequest): Promise<AssetMappingResponse> => {
  // Simulate AI processing delay
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Return mock classification results
  // TODO: Integrate with actual ML model (TensorFlow.js, custom CNN model, etc.)
  return {
    landUseClassification: {
      forest: 45.2,
      water: 8.7,
      agricultural: 32.1,
      urban: 6.3,
      barren: 7.7,
    },
    confidence: 91.3,
    processedImageUrl: 'https://images.pexels.com/photos/1005644/pexels-photo-1005644.jpeg?auto=compress&cs=tinysrgb&w=800'
  };
};

// Mock DSS API
export const generateDSSRecommendations = async (request: DSSRequest): Promise<DSSResponse> => {
  // Simulate AI analysis delay
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Return rule-based recommendations
  // TODO: Integrate with actual ML recommendation engine
  return {
    recommendations: [
      {
        schemeName: 'Jal Jeevan Mission',
        eligibilityScore: 89,
        reasoning: 'Low water availability index indicates need for water infrastructure',
        priority: 'high'
      },
      {
        schemeName: 'PM-KISAN Scheme',
        eligibilityScore: 92,
        reasoning: 'High agricultural dependency makes farmers eligible for income support',
        priority: 'high'
      }
    ]
  };
};

// Utility function to simulate API calls
export const simulateAPICall = async (delay: number = 1000) => {
  return new Promise(resolve => setTimeout(resolve, delay));
};