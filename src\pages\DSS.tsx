import React, { useState } from 'react';
import { Brain, MapPin, TrendingUp, Users, Droplets, Sprout, Award } from 'lucide-react';
import { villagesData, schemesData } from '../data/mockData';

interface VillageStats {
  village: string;
  district: string;
  state: string;
  population: number;
  forestCover: number;
  waterIndex: number;
  agriculturePercentage: number;
  literacyRate: number;
  averageIncome: number;
}

interface Recommendation {
  scheme: {
    id: string;
    name: string;
    description: string;
    ministry: string;
    eligibilityScore: number;
  };
  reason: string;
  priority: 'high' | 'medium' | 'low';
  impact: string;
}

const DSS: React.FC = () => {
  const [selectedVillage, setSelectedVillage] = useState<string>('');
  const [villageStats, setVillageStats] = useState<VillageStats | null>(null);
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const generateRecommendations = async (villageId: string) => {
    setIsGenerating(true);
    
    // Simulate AI recommendation generation
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const village = villagesData.find(v => v.id === villageId);
    if (!village) return;
    
    setVillageStats(village.stats);
    
    // Rule-based recommendations based on village stats
    const generatedRecommendations: Recommendation[] = [];
    
    if (village.stats.waterIndex < 50) {
      generatedRecommendations.push({
        scheme: schemesData.find(s => s.id === 'jal-jeevan')!,
        reason: `Low water availability index (${village.stats.waterIndex}%) indicates need for water infrastructure`,
        priority: 'high',
        impact: 'Direct improvement in water access for 300+ households'
      });
    }
    
    if (village.stats.agriculturePercentage > 60) {
      generatedRecommendations.push({
        scheme: schemesData.find(s => s.id === 'pm-kisan')!,
        reason: `High agricultural dependency (${village.stats.agriculturePercentage}%) makes farmers eligible for income support`,
        priority: 'high',
        impact: 'Financial support for 150+ farming families'
      });
    }
    
    if (village.stats.forestCover > 40) {
      generatedRecommendations.push({
        scheme: schemesData.find(s => s.id === 'nrega')!,
        reason: `Significant forest cover (${village.stats.forestCover}%) creates opportunities for eco-restoration work`,
        priority: 'medium',
        impact: 'Employment generation through forest conservation activities'
      });
    }
    
    if (village.stats.literacyRate < 70) {
      generatedRecommendations.push({
        scheme: schemesData.find(s => s.id === 'samagra-shiksha')!,
        reason: `Literacy rate below national average (${village.stats.literacyRate}%) requires educational intervention`,
        priority: 'medium',
        impact: 'Educational infrastructure development for 200+ children'
      });
    }
    
    setRecommendations(generatedRecommendations);
    setIsGenerating(false);
  };

  const priorityColors = {
    high: 'bg-red-100 text-red-800 border-red-200',
    medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    low: 'bg-green-100 text-green-800 border-green-200',
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-slate-800 mb-2">Decision Support System</h1>
        <p className="text-slate-600">
          AI-powered analysis and recommendations for village development schemes
        </p>
      </div>

      {/* Village Selection */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
        <h2 className="text-lg font-semibold text-slate-800 mb-4 flex items-center">
          <MapPin className="w-5 h-5 mr-2 text-blue-600" />
          Select Village for Analysis
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">Choose Village</label>
            <select
              value={selectedVillage}
              onChange={(e) => {
                setSelectedVillage(e.target.value);
                if (e.target.value) {
                  generateRecommendations(e.target.value);
                } else {
                  setVillageStats(null);
                  setRecommendations([]);
                }
              }}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select a village...</option>
              {villagesData.map(village => (
                <option key={village.id} value={village.id}>
                  {village.name}, {village.district}, {village.state}
                </option>
              ))}
            </select>
          </div>
          
          {selectedVillage && (
            <div className="flex items-end">
              <button
                onClick={() => generateRecommendations(selectedVillage)}
                disabled={isGenerating}
                className="flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-slate-400 transition-all duration-200"
              >
                {isGenerating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Analyzing...
                  </>
                ) : (
                  <>
                    <Brain className="w-4 h-4 mr-2" />
                    Generate Analysis
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Village Statistics */}
      {villageStats && (
        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <h2 className="text-lg font-semibold text-slate-800 mb-6 flex items-center">
            <TrendingUp className="w-5 h-5 mr-2 text-green-600" />
            Village Socio-Economic Profile
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              { label: 'Population', value: villageStats.population.toLocaleString(), icon: Users, color: 'text-blue-600' },
              { label: 'Forest Cover', value: `${villageStats.forestCover}%`, icon: Sprout, color: 'text-green-600' },
              { label: 'Water Index', value: `${villageStats.waterIndex}%`, icon: Droplets, color: 'text-blue-500' },
              { label: 'Agriculture', value: `${villageStats.agriculturePercentage}%`, icon: Sprout, color: 'text-yellow-600' },
              { label: 'Literacy Rate', value: `${villageStats.literacyRate}%`, icon: Award, color: 'text-purple-600' },
              { label: 'Avg. Income', value: `₹${villageStats.averageIncome.toLocaleString()}`, icon: TrendingUp, color: 'text-orange-600' },
            ].map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={index} className="bg-slate-50 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <Icon className={`w-6 h-6 ${stat.color}`} />
                    <div>
                      <p className="text-lg font-bold text-slate-800">{stat.value}</p>
                      <p className="text-sm text-slate-600">{stat.label}</p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <h2 className="text-lg font-semibold text-slate-800 mb-6 flex items-center">
            <Brain className="w-5 h-5 mr-2 text-purple-600" />
            AI-Generated Scheme Recommendations
          </h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {recommendations.map((rec, index) => (
              <div key={index} className="border border-slate-200 rounded-lg p-5 hover:shadow-md transition-shadow duration-200">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h3 className="font-semibold text-slate-800 mb-1">{rec.scheme.name}</h3>
                    <p className="text-xs text-slate-500 mb-2">{rec.scheme.ministry}</p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${priorityColors[rec.priority]}`}>
                    {rec.priority.toUpperCase()} PRIORITY
                  </span>
                </div>
                
                <p className="text-sm text-slate-700 mb-3">{rec.scheme.description}</p>
                
                <div className="space-y-2 text-sm">
                  <div className="bg-blue-50 border-l-4 border-blue-400 p-3 rounded">
                    <p className="font-medium text-blue-900 mb-1">Why this scheme?</p>
                    <p className="text-blue-800">{rec.reason}</p>
                  </div>
                  
                  <div className="bg-green-50 border-l-4 border-green-400 p-3 rounded">
                    <p className="font-medium text-green-900 mb-1">Expected Impact</p>
                    <p className="text-green-800">{rec.impact}</p>
                  </div>
                </div>
                
                <div className="mt-4 flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-slate-600">Eligibility Score:</span>
                    <span className="text-sm font-bold text-green-600">{rec.scheme.eligibilityScore}%</span>
                  </div>
                  <button className="text-xs px-3 py-1 bg-slate-100 text-slate-700 rounded-full hover:bg-slate-200 transition-colors duration-200">
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* AI Analysis Info */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200 p-6">
        <div className="text-center">
          <Brain className="w-8 h-8 text-purple-600 mx-auto mb-3" />
          <h3 className="font-semibold text-slate-800 mb-2">How Our AI Makes Recommendations</h3>
          <p className="text-sm text-slate-600 max-w-3xl mx-auto">
            Our decision support system analyzes village demographics, geographical data, forest coverage, 
            water availability, and socio-economic indicators to match the most suitable government schemes. 
            The AI considers eligibility criteria, potential impact, and local needs to prioritize recommendations.
          </p>
        </div>
      </div>
    </div>
  );
};

export default DSS;