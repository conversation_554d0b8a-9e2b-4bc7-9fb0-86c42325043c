// Mock data for FRA claims
export const fraClaimsData = [
  {
    id: '1',
    holderName: '<PERSON><PERSON>',
    village: 'Jhiripani',
    district: 'Rayagada',
    state: 'Odisha',
    claimType: 'IFR' as const,
    coordinates: [19.1626, 83.4153] as [number, number],
    area: 2.5,
    status: 'Approved' as const,
  },
  {
    id: '2',
    holderName: 'Sunita Devi',
    village: 'Keshkal',
    district: 'Kondagaon',
    state: 'Chhattisgarh',
    claimType: 'CFR' as const,
    coordinates: [19.8943, 81.6623] as [number, number],
    area: 15.2,
    status: 'Pending' as const,
  },
  {
    id: '3',
    holderName: '<PERSON><PERSON><PERSON>',
    village: 'Mandla',
    district: 'Mandla',
    state: 'Madhya Pradesh',
    claimType: 'CR' as const,
    coordinates: [22.5973, 80.3700] as [number, number],
    area: 8.7,
    status: 'Approved' as const,
  },
  {
    id: '4',
    holderName: 'Kamala Das',
    village: 'Agartala Rural',
    district: 'West Tripura',
    state: 'Tripura',
    claimType: 'IFR' as const,
    coordinates: [23.8315, 91.2868] as [number, number],
    area: 3.2,
    status: 'Pending' as const,
  },
  {
    id: '5',
    holderName: 'Ravi Naik',
    village: 'Warangal Rural',
    district: 'Warangal',
    state: 'Telangana',
    claimType: 'CFR' as const,
    coordinates: [17.9692, 79.5926] as [number, number],
    area: 12.1,
    status: 'Approved' as const,
  },
];

// Mock state boundary data (simplified coordinates)
export const stateData = [
  {
    name: 'Odisha',
    coordinates: [[19.5, 83.0], [19.8, 83.5], [20.2, 84.0], [19.9, 84.5], [19.3, 84.2], [19.0, 83.7], [19.2, 83.2]] as [number, number][],
  },
  {
    name: 'Madhya Pradesh',
    coordinates: [[22.0, 80.0], [22.5, 80.5], [23.0, 81.0], [22.8, 81.5], [22.3, 81.2], [21.8, 80.7], [21.5, 80.2]] as [number, number][],
  },
  {
    name: 'Telangana',
    coordinates: [[17.5, 79.0], [18.0, 79.5], [18.5, 80.0], [18.2, 80.5], [17.7, 80.2], [17.3, 79.7], [17.1, 79.2]] as [number, number][],
  },
];

// Mock villages data for DSS
export const villagesData = [
  {
    id: 'jhiripani-odisha',
    name: 'Jhiripani',
    district: 'Rayagada',
    state: 'Odisha',
    stats: {
      village: 'Jhiripani',
      district: 'Rayagada',
      state: 'Odisha',
      population: 2456,
      forestCover: 68.5,
      waterIndex: 34.2,
      agriculturePercentage: 72.1,
      literacyRate: 58.3,
      averageIncome: 45000,
    }
  },
  {
    id: 'keshkal-cg',
    name: 'Keshkal',
    district: 'Kondagaon',
    state: 'Chhattisgarh',
    stats: {
      village: 'Keshkal',
      district: 'Kondagaon',
      state: 'Chhattisgarh',
      population: 3821,
      forestCover: 78.9,
      waterIndex: 67.4,
      agriculturePercentage: 45.6,
      literacyRate: 64.7,
      averageIncome: 52000,
    }
  },
  {
    id: 'mandla-mp',
    name: 'Mandla',
    district: 'Mandla',
    state: 'Madhya Pradesh',
    stats: {
      village: 'Mandla',
      district: 'Mandla',
      state: 'Madhya Pradesh',
      population: 1987,
      forestCover: 82.3,
      waterIndex: 45.8,
      agriculturePercentage: 68.9,
      literacyRate: 71.2,
      averageIncome: 48000,
    }
  },
  {
    id: 'agartala-tripura',
    name: 'Agartala Rural',
    district: 'West Tripura',
    state: 'Tripura',
    stats: {
      village: 'Agartala Rural',
      district: 'West Tripura',
      state: 'Tripura',
      population: 1654,
      forestCover: 56.7,
      waterIndex: 78.3,
      agriculturePercentage: 38.4,
      literacyRate: 79.6,
      averageIncome: 55000,
    }
  },
  {
    id: 'warangal-ts',
    name: 'Warangal Rural',
    district: 'Warangal',
    state: 'Telangana',
    stats: {
      village: 'Warangal Rural',
      district: 'Warangal',
      state: 'Telangana',
      population: 3245,
      forestCover: 42.1,
      waterIndex: 58.9,
      agriculturePercentage: 84.2,
      literacyRate: 67.8,
      averageIncome: 51000,
    }
  },
];

// Mock schemes data
export const schemesData = [
  {
    id: 'jal-jeevan',
    name: 'Jal Jeevan Mission',
    description: 'Provides functional household tap connection to every rural household. Focus on water quality, sustainability, and community ownership.',
    ministry: 'Ministry of Jal Shakti',
    eligibilityScore: 89,
  },
  {
    id: 'pm-kisan',
    name: 'PM-KISAN Scheme',
    description: 'Income support of ₹6,000 per year to all farmer families holding cultivable land. Direct benefit transfer to farmer accounts.',
    ministry: 'Ministry of Agriculture & Farmers Welfare',
    eligibilityScore: 92,
  },
  {
    id: 'nrega',
    name: 'MGNREGA',
    description: 'Guarantees 100 days of wage employment to rural households. Focus on water conservation, afforestation, and infrastructure development.',
    ministry: 'Ministry of Rural Development',
    eligibilityScore: 76,
  },
  {
    id: 'samagra-shiksha',
    name: 'Samagra Shiksha',
    description: 'Integrated scheme for school education covering pre-school to class XII. Aims to improve quality and accessibility of education.',
    ministry: 'Ministry of Education',
    eligibilityScore: 84,
  },
  {
    id: 'swachh-bharat',
    name: 'Swachh Bharat Mission',
    description: 'Clean India mission focusing on sanitation, waste management, and behavioral change for health and dignity.',
    ministry: 'Ministry of Housing and Urban Affairs',
    eligibilityScore: 71,
  },
];